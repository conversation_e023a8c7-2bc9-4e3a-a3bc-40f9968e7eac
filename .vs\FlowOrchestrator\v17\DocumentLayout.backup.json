{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Design50\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{38D6F211-BD74-4399-B71C-739D966B6A7F}|Managers\\Manager.Step\\Manager.Step.csproj|c:\\users\\<USER>\\source\\repos\\design50\\managers\\manager.step\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{38D6F211-BD74-4399-B71C-739D966B6A7F}|Managers\\Manager.Step\\Manager.Step.csproj|solutionrelative:managers\\manager.step\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{38D6F211-BD74-4399-B71C-739D966B6A7F}|Managers\\Manager.Step\\Manager.Step.csproj|c:\\users\\<USER>\\source\\repos\\design50\\managers\\manager.step\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{38D6F211-BD74-4399-B71C-739D966B6A7F}|Managers\\Manager.Step\\Manager.Step.csproj|solutionrelative:managers\\manager.step\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{38D6F211-BD74-4399-B71C-739D966B6A7F}|Managers\\Manager.Step\\Manager.Step.csproj|c:\\users\\<USER>\\source\\repos\\design50\\managers\\manager.step\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{38D6F211-BD74-4399-B71C-739D966B6A7F}|Managers\\Manager.Step\\Manager.Step.csproj|solutionrelative:managers\\manager.step\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{5D281A9C-205C-428E-8BE3-7C1D18C7F784}|Managers\\Manager.Address\\Manager.Address.csproj|c:\\users\\<USER>\\source\\repos\\design50\\managers\\manager.address\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5D281A9C-205C-428E-8BE3-7C1D18C7F784}|Managers\\Manager.Address\\Manager.Address.csproj|solutionrelative:managers\\manager.address\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|c:\\users\\<USER>\\source\\repos\\design50\\managers\\manager.orchestrator\\jobs\\orchestratedflowjob.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|solutionrelative:managers\\manager.orchestrator\\jobs\\orchestratedflowjob.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C306D9CB-FFF8-41E3-AB51-94F3E339A9BB}|Processors\\Processor.File\\Processor.File.csproj|c:\\users\\<USER>\\source\\repos\\design50\\processors\\processor.file\\services\\processorfilemetricsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C306D9CB-FFF8-41E3-AB51-94F3E339A9BB}|Processors\\Processor.File\\Processor.File.csproj|solutionrelative:processors\\processor.file\\services\\processorfilemetricsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C306D9CB-FFF8-41E3-AB51-94F3E339A9BB}|Processors\\Processor.File\\Processor.File.csproj|c:\\users\\<USER>\\source\\repos\\design50\\processors\\processor.file\\fileprocessorapplication.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C306D9CB-FFF8-41E3-AB51-94F3E339A9BB}|Processors\\Processor.File\\Processor.File.csproj|solutionrelative:processors\\processor.file\\fileprocessorapplication.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{23599D12-4B26-4411-8317-BA26063D1145}|Shared\\Shared.Configuration\\Shared.Configuration.csproj|c:\\users\\<USER>\\source\\repos\\design50\\shared\\shared.configuration\\opentelemetryconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{23599D12-4B26-4411-8317-BA26063D1145}|Shared\\Shared.Configuration\\Shared.Configuration.csproj|solutionrelative:shared\\shared.configuration\\opentelemetryconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|c:\\users\\<USER>\\source\\repos\\design50\\managers\\manager.orchestrator\\services\\orchestrationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|solutionrelative:managers\\manager.orchestrator\\services\\orchestrationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Step\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "Managers\\Manager.Step\\Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Step\\Properties\\launchSettings.json", "RelativeToolTip": "Managers\\Manager.Step\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-26T12:25:37.095Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Step\\appsettings.json", "RelativeDocumentMoniker": "Managers\\Manager.Step\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Step\\appsettings.json", "RelativeToolTip": "Managers\\Manager.Step\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-26T12:25:31.471Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Step\\Program.cs", "RelativeDocumentMoniker": "Managers\\Manager.Step\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Step\\Program.cs*", "RelativeToolTip": "Managers\\Manager.Step\\Program.cs*", "ViewState": "AgIAADAAAAAAAAAAAAAAAD8AAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T12:25:26.28Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Address\\Program.cs", "RelativeDocumentMoniker": "Managers\\Manager.Address\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Address\\Program.cs", "RelativeToolTip": "Managers\\Manager.Address\\Program.cs", "ViewState": "AgIAAC0AAAAAAAAAAAAAAD8AAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T12:23:55.105Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ProcessorFileMetricsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design50\\Processors\\Processor.File\\Services\\ProcessorFileMetricsService.cs", "RelativeDocumentMoniker": "Processors\\Processor.File\\Services\\ProcessorFileMetricsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design50\\Processors\\Processor.File\\Services\\ProcessorFileMetricsService.cs", "RelativeToolTip": "Processors\\Processor.File\\Services\\ProcessorFileMetricsService.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAowDkAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T07:59:40.721Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "FileProcessorApplication.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design50\\Processors\\Processor.File\\FileProcessorApplication.cs", "RelativeDocumentMoniker": "Processors\\Processor.File\\FileProcessorApplication.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design50\\Processors\\Processor.File\\FileProcessorApplication.cs", "RelativeToolTip": "Processors\\Processor.File\\FileProcessorApplication.cs", "ViewState": "AgIAAC0AAAAAAAAAAAAYwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T07:59:34.829Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "OpenTelemetryConfiguration.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design50\\Shared\\Shared.Configuration\\OpenTelemetryConfiguration.cs", "RelativeDocumentMoniker": "Shared\\Shared.Configuration\\OpenTelemetryConfiguration.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design50\\Shared\\Shared.Configuration\\OpenTelemetryConfiguration.cs", "RelativeToolTip": "Shared\\Shared.Configuration\\OpenTelemetryConfiguration.cs", "ViewState": "AgIAADoAAAAAAAAAAAAQwEcAAABDAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T07:49:59.717Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "OrchestratedFlowJob.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Orchestrator\\Jobs\\OrchestratedFlowJob.cs", "RelativeDocumentMoniker": "Managers\\Manager.Orchestrator\\Jobs\\OrchestratedFlowJob.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Orchestrator\\Jobs\\OrchestratedFlowJob.cs", "RelativeToolTip": "Managers\\Manager.Orchestrator\\Jobs\\OrchestratedFlowJob.cs", "ViewState": "AgIAAGIAAAAAAAAAAAAcwHAAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T04:12:14.535Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "OrchestrationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Orchestrator\\Services\\OrchestrationService.cs", "RelativeDocumentMoniker": "Managers\\Manager.Orchestrator\\Services\\OrchestrationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Orchestrator\\Services\\OrchestrationService.cs", "RelativeToolTip": "Managers\\Manager.Orchestrator\\Services\\OrchestrationService.cs", "ViewState": "AgIAAG4BAAAAAAAAAAAUwHwBAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T03:44:31.547Z", "EditorCaption": ""}]}]}]}