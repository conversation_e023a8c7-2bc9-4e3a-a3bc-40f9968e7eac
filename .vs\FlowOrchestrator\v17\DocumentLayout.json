{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Design50\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{F39A6F58-AF90-4FF5-BC7B-A10222C615C5}|Shared\\Shared.Models\\Shared.Models.csproj|c:\\users\\<USER>\\source\\repos\\design50\\shared\\shared.models\\healthstatusresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F39A6F58-AF90-4FF5-BC7B-A10222C615C5}|Shared\\Shared.Models\\Shared.Models.csproj|solutionrelative:shared\\shared.models\\healthstatusresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|c:\\users\\<USER>\\source\\repos\\design50\\managers\\manager.orchestrator\\services\\orchestrationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|solutionrelative:managers\\manager.orchestrator\\services\\orchestrationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|c:\\users\\<USER>\\source\\repos\\design50\\managers\\manager.orchestrator\\services\\iorchestrationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|solutionrelative:managers\\manager.orchestrator\\services\\iorchestrationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|c:\\users\\<USER>\\source\\repos\\design50\\managers\\manager.orchestrator\\controllers\\orchestrationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|solutionrelative:managers\\manager.orchestrator\\controllers\\orchestrationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F39A6F58-AF90-4FF5-BC7B-A10222C615C5}|Shared\\Shared.Models\\Shared.Models.csproj|c:\\users\\<USER>\\source\\repos\\design50\\shared\\shared.models\\processorhealthcacheentry.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F39A6F58-AF90-4FF5-BC7B-A10222C615C5}|Shared\\Shared.Models\\Shared.Models.csproj|solutionrelative:shared\\shared.models\\processorhealthcacheentry.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "HealthStatusResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design50\\Shared\\Shared.Models\\HealthStatusResponse.cs", "RelativeDocumentMoniker": "Shared\\Shared.Models\\HealthStatusResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design50\\Shared\\Shared.Models\\HealthStatusResponse.cs", "RelativeToolTip": "Shared\\Shared.Models\\HealthStatusResponse.cs", "ViewState": "AgIAAEoAAAAAAAAAAAAUwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T04:06:59.244Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "IOrchestrationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Orchestrator\\Services\\IOrchestrationService.cs", "RelativeDocumentMoniker": "Managers\\Manager.Orchestrator\\Services\\IOrchestrationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Orchestrator\\Services\\IOrchestrationService.cs", "RelativeToolTip": "Managers\\Manager.Orchestrator\\Services\\IOrchestrationService.cs", "ViewState": "AgIAABcAAAAAAAAAAAA1wCUAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T04:04:34.284Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "OrchestrationController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Orchestrator\\Controllers\\OrchestrationController.cs", "RelativeDocumentMoniker": "Managers\\Manager.Orchestrator\\Controllers\\OrchestrationController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Orchestrator\\Controllers\\OrchestrationController.cs", "RelativeToolTip": "Managers\\Manager.Orchestrator\\Controllers\\OrchestrationController.cs", "ViewState": "AgIAALsAAAAAAAAAAAAtwMQAAABLAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T04:00:40.237Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ProcessorHealthCacheEntry.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design50\\Shared\\Shared.Models\\ProcessorHealthCacheEntry.cs", "RelativeDocumentMoniker": "Shared\\Shared.Models\\ProcessorHealthCacheEntry.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design50\\Shared\\Shared.Models\\ProcessorHealthCacheEntry.cs", "RelativeToolTip": "Shared\\Shared.Models\\ProcessorHealthCacheEntry.cs", "ViewState": "AgIAACgAAAAAAAAAAAAkwCwAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T03:56:41.606Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "OrchestrationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Orchestrator\\Services\\OrchestrationService.cs", "RelativeDocumentMoniker": "Managers\\Manager.Orchestrator\\Services\\OrchestrationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design50\\Managers\\Manager.Orchestrator\\Services\\OrchestrationService.cs", "RelativeToolTip": "Managers\\Manager.Orchestrator\\Services\\OrchestrationService.cs", "ViewState": "AgIAAIgBAAAAAAAAAAAtwJwBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T03:44:31.547Z", "EditorCaption": ""}]}]}]}