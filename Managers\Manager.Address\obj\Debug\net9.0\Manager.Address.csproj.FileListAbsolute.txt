C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\appsettings.json
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Development.json
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Production.json
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.exe
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.deps.json
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.runtimeconfig.json
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.MongoDb.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.Rabbitmq.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.Core.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.SecurityToken.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\DnsClient.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Hazelcast.Net.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.Abstractions.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.RabbitMqTransport.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.Core.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Libmongocrypt.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.ProviderBuilderExtensions.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.Console.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.OpenTelemetryProtocol.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.AspNetCore.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Http.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Runtime.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Polly.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Polly.Core.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Polly.Extensions.Http.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\RabbitMQ.Client.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\SharpCompress.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Snappier.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Annotations.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\System.Text.Json.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\ZstdSharp.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\runtimes\linux\native\libmongocrypt.so
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\runtimes\osx\native\libmongocrypt.dylib
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\runtimes\win\native\mongocrypt.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.pdb
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.xml
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.pdb
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.xml
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.pdb
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.xml
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.pdb
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.xml
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.pdb
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.xml
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.pdb
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.xml
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.pdb
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.xml
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.pdb
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.xml
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.pdb
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.xml
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.pdb
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.pdb
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.xml
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfo.cs
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\scopedcss\bundle\Manager.Address.styles.css
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.Manager.Address.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.Manager.Address.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.build.Manager.Address.props
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.buildMultiTargeting.Manager.Address.props
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.buildTransitive.Manager.Address.props
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.pack.json
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.upToDateCheck.txt
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\Manager..8BB275AE.Up2Date
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\refint\Manager.Address.dll
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.genruntimeconfig.cache
C:\Users\<USER>\source\repos\Design41\Managers\Manager.Address\obj\Debug\net9.0\ref\Manager.Address.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\appsettings.json
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Development.json
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Production.json
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.exe
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.deps.json
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.runtimeconfig.json
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.MongoDb.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.Rabbitmq.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.Core.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.SecurityToken.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\DnsClient.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Hazelcast.Net.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.Abstractions.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.RabbitMqTransport.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.Core.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Libmongocrypt.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.ProviderBuilderExtensions.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.Console.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.OpenTelemetryProtocol.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.AspNetCore.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Http.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Runtime.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Polly.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Polly.Core.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Polly.Extensions.Http.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\RabbitMQ.Client.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\SharpCompress.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Snappier.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Annotations.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\System.Text.Json.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\ZstdSharp.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\runtimes\linux\native\libmongocrypt.so
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\runtimes\osx\native\libmongocrypt.dylib
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\runtimes\win\native\mongocrypt.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.pdb
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.xml
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.pdb
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.xml
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.pdb
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.xml
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.pdb
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.xml
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.pdb
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.xml
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.pdb
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.xml
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.pdb
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.xml
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.pdb
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.xml
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.pdb
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.xml
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.pdb
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.pdb
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.xml
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfo.cs
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\scopedcss\bundle\Manager.Address.styles.css
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.Manager.Address.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.Manager.Address.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.build.Manager.Address.props
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.buildMultiTargeting.Manager.Address.props
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.buildTransitive.Manager.Address.props
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.pack.json
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\Manager..8BB275AE.Up2Date
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\refint\Manager.Address.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.genruntimeconfig.cache
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\ref\Manager.Address.dll
C:\Users\<USER>\source\repos\Design44\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.upToDateCheck.txt
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\appsettings.json
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Development.json
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Production.json
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.exe
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.deps.json
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.runtimeconfig.json
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.MongoDb.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.Rabbitmq.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.Core.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.SecurityToken.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\DnsClient.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Hazelcast.Net.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.Abstractions.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.RabbitMqTransport.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.Core.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Libmongocrypt.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.ProviderBuilderExtensions.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.Console.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.OpenTelemetryProtocol.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.AspNetCore.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Http.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Runtime.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Polly.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Polly.Core.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Polly.Extensions.Http.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\RabbitMQ.Client.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\SharpCompress.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Snappier.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Annotations.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\System.Text.Json.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\ZstdSharp.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\runtimes\linux\native\libmongocrypt.so
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\runtimes\osx\native\libmongocrypt.dylib
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\runtimes\win\native\mongocrypt.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.pdb
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.xml
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.pdb
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.xml
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.pdb
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.xml
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.pdb
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.xml
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.pdb
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.xml
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.pdb
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.xml
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.pdb
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.xml
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.pdb
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.xml
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.pdb
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.xml
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.pdb
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.pdb
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.xml
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfo.cs
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\scopedcss\bundle\Manager.Address.styles.css
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.Manager.Address.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.Manager.Address.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.build.Manager.Address.props
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.buildMultiTargeting.Manager.Address.props
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.buildTransitive.Manager.Address.props
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.pack.json
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.upToDateCheck.txt
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\Manager..8BB275AE.Up2Date
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\refint\Manager.Address.dll
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.genruntimeconfig.cache
C:\Users\<USER>\source\repos\Design45\Managers\Manager.Address\obj\Debug\net9.0\ref\Manager.Address.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\appsettings.json
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Development.json
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Production.json
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.exe
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.deps.json
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.runtimeconfig.json
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.MongoDb.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.Rabbitmq.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.Core.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.SecurityToken.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\DnsClient.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Hazelcast.Net.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.Abstractions.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.RabbitMqTransport.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.Core.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Libmongocrypt.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.ProviderBuilderExtensions.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.Console.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.OpenTelemetryProtocol.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.AspNetCore.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Http.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Runtime.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Polly.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Polly.Core.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Polly.Extensions.Http.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\RabbitMQ.Client.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\SharpCompress.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Snappier.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Annotations.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\System.Text.Json.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\ZstdSharp.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\runtimes\linux\native\libmongocrypt.so
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\runtimes\osx\native\libmongocrypt.dylib
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\runtimes\win\native\mongocrypt.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.pdb
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.xml
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.pdb
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.xml
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.pdb
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.xml
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.pdb
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.xml
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.pdb
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.xml
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.pdb
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.xml
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.pdb
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.xml
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.pdb
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.xml
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.pdb
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.xml
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.pdb
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.pdb
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.xml
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfo.cs
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\scopedcss\bundle\Manager.Address.styles.css
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.Manager.Address.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.Manager.Address.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.build.Manager.Address.props
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.buildMultiTargeting.Manager.Address.props
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.buildTransitive.Manager.Address.props
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.pack.json
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\Manager..8BB275AE.Up2Date
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\refint\Manager.Address.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.genruntimeconfig.cache
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\ref\Manager.Address.dll
C:\Users\<USER>\source\repos\Design46\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.upToDateCheck.txt
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\appsettings.json
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Development.json
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Production.json
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.exe
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.deps.json
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.runtimeconfig.json
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.MongoDb.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.Rabbitmq.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.Core.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.SecurityToken.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\DnsClient.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Hazelcast.Net.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.Abstractions.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.RabbitMqTransport.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.Core.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Libmongocrypt.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.ProviderBuilderExtensions.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.Console.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.OpenTelemetryProtocol.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.AspNetCore.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Http.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Runtime.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Polly.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Polly.Core.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Polly.Extensions.Http.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\RabbitMQ.Client.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\SharpCompress.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Snappier.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Annotations.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\System.Text.Json.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\ZstdSharp.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\runtimes\linux\native\libmongocrypt.so
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\runtimes\osx\native\libmongocrypt.dylib
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\runtimes\win\native\mongocrypt.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.pdb
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.xml
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.pdb
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.xml
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.pdb
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.xml
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.pdb
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.xml
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.pdb
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.xml
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.pdb
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.xml
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.pdb
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.xml
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.pdb
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.xml
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.pdb
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.xml
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.pdb
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.pdb
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.xml
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfo.cs
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\scopedcss\bundle\Manager.Address.styles.css
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.Manager.Address.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.Manager.Address.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.build.Manager.Address.props
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.buildMultiTargeting.Manager.Address.props
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.buildTransitive.Manager.Address.props
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.pack.json
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.upToDateCheck.txt
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\Manager..8BB275AE.Up2Date
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\refint\Manager.Address.dll
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.genruntimeconfig.cache
C:\Users\<USER>\source\repos\Design47\Managers\Manager.Address\obj\Debug\net9.0\ref\Manager.Address.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\appsettings.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Development.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Production.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.exe
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.deps.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.runtimeconfig.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.MongoDb.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.Rabbitmq.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.Core.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.SecurityToken.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\DnsClient.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Hazelcast.Net.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.Abstractions.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.RabbitMqTransport.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.Core.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Libmongocrypt.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.ProviderBuilderExtensions.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.Console.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.OpenTelemetryProtocol.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.AspNetCore.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Http.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Runtime.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Polly.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Polly.Core.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Polly.Extensions.Http.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\RabbitMQ.Client.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\SharpCompress.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Snappier.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Annotations.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\ZstdSharp.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\runtimes\linux\native\libmongocrypt.so
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\runtimes\osx\native\libmongocrypt.dylib
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\runtimes\win\native\mongocrypt.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfo.cs
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\scopedcss\bundle\Manager.Address.styles.css
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager..8BB275AE.Up2Date
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\refint\Manager.Address.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.genruntimeconfig.cache
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\ref\Manager.Address.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\rpswa.dswa.cache.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\rjimswa.dswa.cache.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\rjsmrazor.dswa.cache.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\rjsmcshtml.dswa.cache.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.json.cache
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.upToDateCheck.txt
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\appsettings.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Development.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Production.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.exe
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.deps.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.runtimeconfig.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.MongoDb.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.Rabbitmq.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.Core.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.SecurityToken.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\DnsClient.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Hazelcast.Net.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.Abstractions.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.RabbitMqTransport.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.Core.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Libmongocrypt.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.ProviderBuilderExtensions.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.Console.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.OpenTelemetryProtocol.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.AspNetCore.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Http.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Runtime.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Polly.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Polly.Core.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Polly.Extensions.Http.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\RabbitMQ.Client.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\SharpCompress.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Snappier.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Annotations.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\System.Text.Json.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\ZstdSharp.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\runtimes\linux\native\libmongocrypt.so
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\runtimes\osx\native\libmongocrypt.dylib
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\runtimes\win\native\mongocrypt.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfo.cs
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\scopedcss\bundle\Manager.Address.styles.css
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.Manager.Address.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.Manager.Address.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.build.Manager.Address.props
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.buildMultiTargeting.Manager.Address.props
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.buildTransitive.Manager.Address.props
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.pack.json
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.upToDateCheck.txt
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager..8BB275AE.Up2Date
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\refint\Manager.Address.dll
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.genruntimeconfig.cache
C:\Users\<USER>\source\repos\Design48\Managers\Manager.Address\obj\Debug\net9.0\ref\Manager.Address.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\appsettings.json
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Development.json
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Production.json
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.exe
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.deps.json
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.runtimeconfig.json
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.MongoDb.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.Rabbitmq.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.Core.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.SecurityToken.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\DnsClient.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Hazelcast.Net.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.Abstractions.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.RabbitMqTransport.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.Core.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Libmongocrypt.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.ProviderBuilderExtensions.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.Console.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.OpenTelemetryProtocol.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.AspNetCore.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Http.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Runtime.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Polly.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Polly.Core.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Polly.Extensions.Http.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\RabbitMQ.Client.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\SharpCompress.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Snappier.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Annotations.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\System.Text.Json.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\ZstdSharp.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\runtimes\linux\native\libmongocrypt.so
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\runtimes\osx\native\libmongocrypt.dylib
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\runtimes\win\native\mongocrypt.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.pdb
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.xml
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.pdb
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.xml
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.pdb
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.xml
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.pdb
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.xml
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.pdb
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.xml
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.pdb
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.xml
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.pdb
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.xml
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.pdb
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.xml
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.pdb
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.xml
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.pdb
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.pdb
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.xml
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfo.cs
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\scopedcss\bundle\Manager.Address.styles.css
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.Manager.Address.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.Manager.Address.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.build.Manager.Address.props
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.buildMultiTargeting.Manager.Address.props
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.buildTransitive.Manager.Address.props
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.pack.json
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\Manager..8BB275AE.Up2Date
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\refint\Manager.Address.dll
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.genruntimeconfig.cache
C:\Users\<USER>\source\repos\Design49\Managers\Manager.Address\obj\Debug\net9.0\ref\Manager.Address.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\appsettings.json
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Development.json
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\appsettings.Production.json
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.exe
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.deps.json
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.runtimeconfig.json
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.MongoDb.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\HealthChecks.Rabbitmq.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.Core.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\AWSSDK.SecurityToken.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\DnsClient.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Hazelcast.Net.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.Abstractions.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\MassTransit.RabbitMqTransport.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Driver.Core.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\MongoDB.Libmongocrypt.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Api.ProviderBuilderExtensions.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.Console.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Exporter.OpenTelemetryProtocol.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.AspNetCore.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Http.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\OpenTelemetry.Instrumentation.Runtime.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Polly.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Polly.Core.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Polly.Extensions.Http.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\RabbitMQ.Client.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\SharpCompress.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Snappier.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Annotations.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\System.Text.Json.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\ZstdSharp.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\runtimes\linux\native\libmongocrypt.so
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\runtimes\osx\native\libmongocrypt.dylib
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\runtimes\win\native\mongocrypt.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.pdb
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Configuration.xml
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.pdb
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Correlation.xml
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.pdb
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Entities.xml
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.pdb
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Exceptions.xml
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.pdb
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.HealthChecks.xml
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.pdb
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.MassTransit.xml
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.pdb
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.MongoDB.xml
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.pdb
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Repositories.xml
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.pdb
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Services.xml
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Extensions.pdb
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.pdb
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\bin\Debug\net9.0\Shared.Models.xml
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.AssemblyInfo.cs
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\scopedcss\bundle\Manager.Address.styles.css
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.Manager.Address.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.Manager.Address.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.build.Manager.Address.props
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.buildMultiTargeting.Manager.Address.props
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets\msbuild.buildTransitive.Manager.Address.props
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.pack.json
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\staticwebassets.upToDateCheck.txt
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\Manager..8BB275AE.Up2Date
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\refint\Manager.Address.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.xml
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.pdb
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.genruntimeconfig.cache
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\ref\Manager.Address.dll
C:\Users\<USER>\source\repos\Design50\Managers\Manager.Address\obj\Debug\net9.0\Manager.Address.sourcelink.json
