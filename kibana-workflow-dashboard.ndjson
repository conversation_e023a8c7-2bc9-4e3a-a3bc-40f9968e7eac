{"version": "8.11.0", "objects": [{"id": "workflow-step-initiation-index", "type": "index-pattern", "namespaces": ["default"], "attributes": {"title": "otel-logs-*", "timeFieldName": "@timestamp", "fields": "[{\"name\":\"@timestamp\",\"type\":\"date\",\"esTypes\":[\"date\"],\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"message\",\"type\":\"string\",\"esTypes\":[\"text\"],\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"level\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"OrchestratedFlowId\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"StepId\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"ExecutionId\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"ProcessorId\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"WorkflowPhase\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"AssignmentCount\",\"type\":\"number\",\"esTypes\":[\"long\"],\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"CorrelationId\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true}]"}, "references": [], "migrationVersion": {"index-pattern": "7.11.0"}, "coreMigrationVersion": "8.11.0", "typeMigrationVersion": "8.0.0"}, {"id": "correlation-filter-control", "type": "visualization", "namespaces": ["default"], "attributes": {"title": "Correlation ID Filter", "type": "input_control_vis", "params": {"controls": [{"id": "correlation_filter", "fieldName": "CorrelationId", "parent": "", "label": "Correlation ID", "type": "list", "options": {"type": "terms", "multiselect": true, "dynamicOptions": true, "size": 10, "order": "desc"}}], "updateFiltersOnChange": true, "useTimeFilter": true, "pinFilters": false}, "uiStateJSON": "{}", "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"workflow-step-initiation-index\",\"query\":{\"match\":{\"message\":\"Workflow step initiated\"}},\"filter\":[]}"}}, "references": [{"id": "workflow-step-initiation-index", "type": "index-pattern", "name": "kibanaSavedObjectMeta.searchSourceJSON.index"}], "migrationVersion": {"visualization": "8.5.0"}, "coreMigrationVersion": "8.11.0", "typeMigrationVersion": "8.0.0"}, {"id": "orchestrated-flow-filter-control", "type": "visualization", "namespaces": ["default"], "attributes": {"title": "Orchestrated Flow ID Filter", "type": "input_control_vis", "params": {"controls": [{"id": "flow_filter", "fieldName": "OrchestratedFlowId", "parent": "", "label": "Orchestrated Flow ID", "type": "list", "options": {"type": "terms", "multiselect": true, "dynamicOptions": true, "size": 10, "order": "desc"}}], "updateFiltersOnChange": true, "useTimeFilter": true, "pinFilters": false}, "uiStateJSON": "{}", "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"workflow-step-initiation-index\",\"query\":{\"match\":{\"message\":\"Workflow step initiated\"}},\"filter\":[]}"}}, "references": [{"id": "workflow-step-initiation-index", "type": "index-pattern", "name": "kibanaSavedObjectMeta.searchSourceJSON.index"}], "migrationVersion": {"visualization": "8.5.0"}, "coreMigrationVersion": "8.11.0", "typeMigrationVersion": "8.0.0"}, {"id": "workflow-step-visualization", "type": "visualization", "namespaces": ["default"], "attributes": {"title": "Workflow Step Initiation Status by ExecutionId", "type": "line", "params": {"grid": {"categoryLines": false, "valueAxis": "ValueAxis-1"}, "categoryAxes": [{"id": "CategoryAxis-1", "type": "category", "position": "bottom", "show": true, "style": {}, "scale": {"type": "linear"}, "labels": {"show": true, "filter": true, "truncate": 100, "rotate": 45}, "title": {"text": "Execution ID"}}], "valueAxes": [{"id": "ValueAxis-1", "name": "LeftAxis-1", "type": "value", "position": "left", "show": true, "style": {}, "scale": {"type": "linear", "mode": "normal", "min": 0, "max": 1}, "labels": {"show": true, "rotate": 0, "filter": false, "truncate": 100}, "title": {"text": "Step Initiation Status (0=Not Exists, 1=Exists)"}}], "seriesParams": [{"show": true, "type": "line", "mode": "normal", "data": {"label": "Step Status", "id": "1"}, "valueAxis": "ValueAxis-1", "drawLinesBetweenPoints": true, "lineWidth": 2, "showCircles": true}], "addTooltip": true, "addLegend": true, "legendPosition": "right", "times": [], "addTimeMarker": false, "thresholdLine": {"show": false, "value": 10, "width": 1, "style": "full", "color": "#E7664C"}, "labels": {}}, "aggs": [{"id": "1", "enabled": true, "type": "max", "schema": "metric", "params": {"field": "AssignmentCount", "customLabel": "Step Status"}}, {"id": "2", "enabled": true, "type": "terms", "schema": "segment", "params": {"field": "ExecutionId", "orderBy": "1", "order": "desc", "size": 50, "otherBucket": false, "otherBucketLabel": "Other", "missingBucket": false, "missingBucketLabel": "Missing", "customLabel": "Execution ID"}}], "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"workflow-step-initiation-index\",\"query\":{\"match\":{\"message\":\"Workflow step initiated\"}},\"filter\":[]}"}}, "references": [{"id": "workflow-step-initiation-index", "type": "index-pattern", "name": "kibanaSavedObjectMeta.searchSourceJSON.index"}], "migrationVersion": {"visualization": "8.5.0"}, "coreMigrationVersion": "8.11.0", "typeMigrationVersion": "8.0.0"}, {"id": "workflow-step-scatter", "type": "visualization", "namespaces": ["default"], "attributes": {"title": "Workflow Step Existence Scatter Plot", "type": "vega-lite", "params": {"spec": "{\"$schema\":\"https://vega.github.io/schema/vega-lite/v5.json\",\"title\":\"Workflow Step Initiation: Exists vs Not Exists by ExecutionId\",\"data\":{\"url\":{\"index\":\"otel-logs-*\",\"body\":{\"query\":{\"bool\":{\"must\":[{\"match\":{\"message\":\"Workflow step initiated\"}},{\"exists\":{\"field\":\"OrchestratedFlowId\"}},{\"exists\":{\"field\":\"ExecutionId\"}},{\"exists\":{\"field\":\"CorrelationId\"}}]}},\"aggs\":{\"execution_groups\":{\"terms\":{\"field\":\"ExecutionId\",\"size\":100},\"aggs\":{\"orchestrated_flow\":{\"terms\":{\"field\":\"OrchestratedFlowId\",\"size\":1}},\"correlation_id\":{\"terms\":{\"field\":\"CorrelationId\",\"size\":1}},\"step_count\":{\"value_count\":{\"field\":\"StepId\"}},\"workflow_phases\":{\"terms\":{\"field\":\"WorkflowPhase\",\"size\":10}},\"assignment_sum\":{\"sum\":{\"field\":\"AssignmentCount\"}}}}},\"size\":0}},\"format\":{\"property\":\"aggregations.execution_groups.buckets\",\"type\":\"json\"}},\"transform\":[{\"calculate\":\"datum.key\",\"as\":\"ExecutionId\"},{\"calculate\":\"datum.step_count.value > 0 ? 1 : 0\",\"as\":\"Status\"},{\"calculate\":\"datum.step_count.value > 0 ? 'Exists' : 'Not Exists'\",\"as\":\"StatusLabel\"},{\"calculate\":\"datum.orchestrated_flow.buckets.length > 0 ? datum.orchestrated_flow.buckets[0].key : 'Unknown'\",\"as\":\"OrchestratedFlowId\"},{\"calculate\":\"datum.correlation_id.buckets.length > 0 ? datum.correlation_id.buckets[0].key : 'Unknown'\",\"as\":\"CorrelationId\"},{\"calculate\":\"datum.assignment_sum.value\",\"as\":\"TotalAssignments\"}],\"mark\":{\"type\":\"circle\",\"size\":150,\"opacity\":0.8},\"encoding\":{\"x\":{\"field\":\"ExecutionId\",\"type\":\"ordinal\",\"title\":\"Execution ID\",\"axis\":{\"labelAngle\":-45,\"labelLimit\":100}},\"y\":{\"field\":\"Status\",\"type\":\"ordinal\",\"title\":\"Step Initiation Status\",\"scale\":{\"domain\":[0,1]},\"axis\":{\"values\":[0,1],\"labelExpr\":\"datum.value == 1 ? 'Exists' : 'Not Exists'\"}},\"color\":{\"field\":\"StatusLabel\",\"type\":\"nominal\",\"scale\":{\"domain\":[\"Exists\",\"Not Exists\"],\"range\":[\"#28a745\",\"#dc3545\"]},\"legend\":{\"title\":\"Step Status\"}},\"size\":{\"field\":\"TotalAssignments\",\"type\":\"quantitative\",\"scale\":{\"range\":[100,400]},\"legend\":{\"title\":\"Total Assignments\"}},\"tooltip\":[{\"field\":\"ExecutionId\",\"type\":\"ordinal\",\"title\":\"Execution ID\"},{\"field\":\"StatusLabel\",\"type\":\"nominal\",\"title\":\"Status\"},{\"field\":\"OrchestratedFlowId\",\"type\":\"ordinal\",\"title\":\"Orchestrated Flow ID\"},{\"field\":\"CorrelationId\",\"type\":\"ordinal\",\"title\":\"Correlation ID\"},{\"field\":\"step_count.value\",\"type\":\"quantitative\",\"title\":\"Step Count\"},{\"field\":\"TotalAssignments\",\"type\":\"quantitative\",\"title\":\"Total Assignments\"}]},\"width\":900,\"height\":500}"}}, "references": [{"id": "workflow-step-initiation-index", "type": "index-pattern", "name": "kibanaSavedObjectMeta.searchSourceJSON.index"}], "migrationVersion": {"visualization": "8.5.0"}, "coreMigrationVersion": "8.11.0", "typeMigrationVersion": "8.0.0"}, {"id": "workflow-step-dashboard", "type": "dashboard", "namespaces": ["default"], "attributes": {"title": "Workflow Step Initiation Dashboard", "hits": 0, "description": "Dashboard showing workflow step initiation status by ExecutionId, grouped by CorrelationId and OrchestratedFlowId. X-axis represents ExecutionId, Y-axis represents existence state (Exists/Not Exists).", "panelsJSON": "[{\"version\":\"8.11.0\",\"gridData\":{\"x\":0,\"y\":0,\"w\":24,\"h\":8,\"i\":\"1\"},\"panelIndex\":\"1\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_1\"},{\"version\":\"8.11.0\",\"gridData\":{\"x\":24,\"y\":0,\"w\":24,\"h\":8,\"i\":\"2\"},\"panelIndex\":\"2\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_2\"},{\"version\":\"8.11.0\",\"gridData\":{\"x\":0,\"y\":8,\"w\":48,\"h\":25,\"i\":\"3\"},\"panelIndex\":\"3\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_3\"},{\"version\":\"8.11.0\",\"gridData\":{\"x\":0,\"y\":33,\"w\":48,\"h\":20,\"i\":\"4\"},\"panelIndex\":\"4\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_4\"}]", "optionsJSON": "{\"useMargins\":true,\"syncColors\":false,\"syncCursor\":true,\"syncTooltips\":true,\"hidePanelTitles\":false}", "version": 1, "timeRestore": false, "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"query\":{\"query\":\"message:\\\"Workflow step initiated\\\"\",\"language\":\"kuery\"},\"filter\":[]}"}}, "references": [{"id": "correlation-filter-control", "type": "visualization", "name": "panel_1"}, {"id": "orchestrated-flow-filter-control", "type": "visualization", "name": "panel_2"}, {"id": "workflow-step-scatter", "type": "visualization", "name": "panel_3"}, {"id": "workflow-step-visualization", "type": "visualization", "name": "panel_4"}], "migrationVersion": {"dashboard": "8.7.0"}, "coreMigrationVersion": "8.11.0", "typeMigrationVersion": "8.0.0"}]}