{"version": "8.11.0", "objects": [{"id": "otel-logs-pattern", "type": "index-pattern", "namespaces": ["default"], "attributes": {"title": "otel-logs-*", "timeFieldName": "@timestamp"}, "references": [], "migrationVersion": {"index-pattern": "7.11.0"}, "coreMigrationVersion": "8.11.0"}, {"id": "workflow-step-viz", "type": "visualization", "namespaces": ["default"], "attributes": {"title": "Workflow Step Status by ExecutionId", "type": "histogram", "params": {"grid": {"categoryLines": false, "valueAxis": "ValueAxis-1"}, "categoryAxes": [{"id": "CategoryAxis-1", "type": "category", "position": "bottom", "show": true, "style": {}, "scale": {"type": "linear"}, "labels": {"show": true, "filter": true, "truncate": 100, "rotate": -45}, "title": {"text": "Execution ID"}}], "valueAxes": [{"id": "ValueAxis-1", "name": "LeftAxis-1", "type": "value", "position": "left", "show": true, "style": {}, "scale": {"type": "linear", "mode": "normal", "min": 0, "max": 1}, "labels": {"show": true, "rotate": 0, "filter": false, "truncate": 100}, "title": {"text": "Step Status (0=Not Exists, 1=Exists)"}}], "seriesParams": [{"show": true, "type": "histogram", "mode": "stacked", "data": {"label": "Step Exists", "id": "1"}, "valueAxis": "ValueAxis-1", "drawLinesBetweenPoints": true, "lineWidth": 2, "showCircles": true}], "addTooltip": true, "addLegend": true, "legendPosition": "right", "times": [], "addTimeMarker": false}, "aggs": [{"id": "1", "enabled": true, "type": "max", "schema": "metric", "params": {"field": "@timestamp", "customLabel": "Step Exists"}}, {"id": "2", "enabled": true, "type": "terms", "schema": "segment", "params": {"field": "ExecutionId.keyword", "orderBy": "1", "order": "desc", "size": 50, "otherBucket": false, "otherBucketLabel": "Other", "missingBucket": false, "missingBucketLabel": "Missing", "customLabel": "Execution ID"}}], "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"otel-logs-pattern\",\"query\":{\"match\":{\"message\":\"Workflow step initiated\"}},\"filter\":[]}"}}, "references": [{"id": "otel-logs-pattern", "type": "index-pattern", "name": "kibanaSavedObjectMeta.searchSourceJSON.index"}], "migrationVersion": {"visualization": "8.5.0"}, "coreMigrationVersion": "8.11.0"}, {"id": "workflow-simple-dashboard", "type": "dashboard", "namespaces": ["default"], "attributes": {"title": "Workflow Step Initiation Status", "hits": 0, "description": "Dashboard showing workflow step initiation by ExecutionId. Filter by CorrelationId and OrchestratedFlowId using the search bar.", "panelsJSON": "[{\"version\":\"8.11.0\",\"gridData\":{\"x\":0,\"y\":0,\"w\":48,\"h\":20,\"i\":\"1\"},\"panelIndex\":\"1\",\"embeddableConfig\":{},\"panelRefName\":\"panel_1\"}]", "optionsJSON": "{\"useMargins\":true,\"syncColors\":false,\"syncCursor\":true,\"syncTooltips\":false,\"hidePanelTitles\":false}", "version": 1, "timeRestore": false, "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"query\":{\"query\":\"message:\\\"Workflow step initiated\\\"\",\"language\":\"kuery\"},\"filter\":[]}"}}, "references": [{"id": "workflow-step-viz", "type": "visualization", "name": "panel_1"}], "migrationVersion": {"dashboard": "8.7.0"}, "coreMigrationVersion": "8.11.0"}]}